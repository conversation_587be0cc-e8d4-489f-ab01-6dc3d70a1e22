{"name": "@napi-rs/nice-win32-x64-msvc", "version": "1.0.1", "cpu": ["x64"], "main": "nice.win32-x64-msvc.node", "files": ["nice.win32-x64-msvc.node"], "description": "https://linux.die.net/man/2/nice binding for Node.js", "keywords": ["napi-rs", "NAPI", "N-API", "Rust", "node-addon", "node-addon-api", "nice"], "license": "MIT", "engines": {"node": ">= 10"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"url": "git+ssh://**************/Brooooooklyn/nice.git", "type": "git"}, "os": ["win32"]}