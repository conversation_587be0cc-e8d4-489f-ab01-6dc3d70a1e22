"use strict";
// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.
// See LICENSE in the project root for license information.
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocNodeTransforms = exports.TSDocParser = exports.TSDocMessageId = exports.TokenSequence = exports.TokenKind = exports.Token = exports.TextRange = exports.ParserMessageLog = exports.ParserMessage = exports.ParserContext = exports.TSDocEmitter = exports.StringBuilder = exports.PlainTextEmitter = exports.ModifierTagSet = exports.StandardModifierTagSet = exports.Standardization = exports.StandardTags = exports.TSDocValidationConfiguration = exports.TSDocTagDefinition = exports.TSDocTagSyntaxKind = exports.TSDocConfiguration = exports.DocNodeManager = void 0;
var DocNodeManager_1 = require("./configuration/DocNodeManager");
Object.defineProperty(exports, "DocNodeManager", { enumerable: true, get: function () { return DocNodeManager_1.DocNodeManager; } });
var TSDocConfiguration_1 = require("./configuration/TSDocConfiguration");
Object.defineProperty(exports, "TSDocConfiguration", { enumerable: true, get: function () { return TSDocConfiguration_1.TSDocConfiguration; } });
var TSDocTagDefinition_1 = require("./configuration/TSDocTagDefinition");
Object.defineProperty(exports, "TSDocTagSyntaxKind", { enumerable: true, get: function () { return TSDocTagDefinition_1.TSDocTagSyntaxKind; } });
Object.defineProperty(exports, "TSDocTagDefinition", { enumerable: true, get: function () { return TSDocTagDefinition_1.TSDocTagDefinition; } });
var TSDocValidationConfiguration_1 = require("./configuration/TSDocValidationConfiguration");
Object.defineProperty(exports, "TSDocValidationConfiguration", { enumerable: true, get: function () { return TSDocValidationConfiguration_1.TSDocValidationConfiguration; } });
var StandardTags_1 = require("./details/StandardTags");
Object.defineProperty(exports, "StandardTags", { enumerable: true, get: function () { return StandardTags_1.StandardTags; } });
var Standardization_1 = require("./details/Standardization");
Object.defineProperty(exports, "Standardization", { enumerable: true, get: function () { return Standardization_1.Standardization; } });
var StandardModifierTagSet_1 = require("./details/StandardModifierTagSet");
Object.defineProperty(exports, "StandardModifierTagSet", { enumerable: true, get: function () { return StandardModifierTagSet_1.StandardModifierTagSet; } });
var ModifierTagSet_1 = require("./details/ModifierTagSet");
Object.defineProperty(exports, "ModifierTagSet", { enumerable: true, get: function () { return ModifierTagSet_1.ModifierTagSet; } });
var PlainTextEmitter_1 = require("./emitters/PlainTextEmitter");
Object.defineProperty(exports, "PlainTextEmitter", { enumerable: true, get: function () { return PlainTextEmitter_1.PlainTextEmitter; } });
var StringBuilder_1 = require("./emitters/StringBuilder");
Object.defineProperty(exports, "StringBuilder", { enumerable: true, get: function () { return StringBuilder_1.StringBuilder; } });
var TSDocEmitter_1 = require("./emitters/TSDocEmitter");
Object.defineProperty(exports, "TSDocEmitter", { enumerable: true, get: function () { return TSDocEmitter_1.TSDocEmitter; } });
__exportStar(require("./nodes"), exports);
var ParserContext_1 = require("./parser/ParserContext");
Object.defineProperty(exports, "ParserContext", { enumerable: true, get: function () { return ParserContext_1.ParserContext; } });
var ParserMessage_1 = require("./parser/ParserMessage");
Object.defineProperty(exports, "ParserMessage", { enumerable: true, get: function () { return ParserMessage_1.ParserMessage; } });
var ParserMessageLog_1 = require("./parser/ParserMessageLog");
Object.defineProperty(exports, "ParserMessageLog", { enumerable: true, get: function () { return ParserMessageLog_1.ParserMessageLog; } });
var TextRange_1 = require("./parser/TextRange");
Object.defineProperty(exports, "TextRange", { enumerable: true, get: function () { return TextRange_1.TextRange; } });
var Token_1 = require("./parser/Token");
Object.defineProperty(exports, "Token", { enumerable: true, get: function () { return Token_1.Token; } });
Object.defineProperty(exports, "TokenKind", { enumerable: true, get: function () { return Token_1.TokenKind; } });
var TokenSequence_1 = require("./parser/TokenSequence");
Object.defineProperty(exports, "TokenSequence", { enumerable: true, get: function () { return TokenSequence_1.TokenSequence; } });
var TSDocMessageId_1 = require("./parser/TSDocMessageId");
Object.defineProperty(exports, "TSDocMessageId", { enumerable: true, get: function () { return TSDocMessageId_1.TSDocMessageId; } });
var TSDocParser_1 = require("./parser/TSDocParser");
Object.defineProperty(exports, "TSDocParser", { enumerable: true, get: function () { return TSDocParser_1.TSDocParser; } });
var DocNodeTransforms_1 = require("./transforms/DocNodeTransforms");
Object.defineProperty(exports, "DocNodeTransforms", { enumerable: true, get: function () { return DocNodeTransforms_1.DocNodeTransforms; } });
//# sourceMappingURL=index.js.map