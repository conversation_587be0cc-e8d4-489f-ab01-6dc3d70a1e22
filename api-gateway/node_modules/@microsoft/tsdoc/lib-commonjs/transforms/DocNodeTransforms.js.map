{"version": 3, "file": "DocNodeTransforms.js", "sourceRoot": "", "sources": ["../../src/transforms/DocNodeTransforms.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D,6DAA4D;AAG5D;;GAEG;AACH;IAAA;IA4CA,CAAC;IA3CC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACW,uCAAqB,GAAnC,UAAoC,YAA0B;QAC5D,OAAO,yCAAmB,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;IACrD,CAAC;IACH,wBAAC;AAAD,CAAC,AA5CD,IA4CC;AA5CY,8CAAiB", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\r\n\r\nimport { TrimSpacesTransform } from './TrimSpacesTransform';\r\nimport type { DocParagraph } from '../nodes';\r\n\r\n/**\r\n * Helper functions that transform DocNode trees.\r\n */\r\nexport class DocNodeTransforms {\r\n  /**\r\n   * trimSpacesInParagraphNodes() collapses extra spacing characters from plain text nodes.\r\n   *\r\n   * @remarks\r\n   * This is useful when emitting HTML, where any number of spaces are equivalent\r\n   * to a single space.  It's also useful when emitting Markdown, where spaces\r\n   * can be misinterpreted as an indented code block.\r\n   *\r\n   * For example, we might transform this:\r\n   *\r\n   * ```\r\n   * nodes: [\r\n   *   { kind: PlainText, text: \"   Here   are some   \" },\r\n   *   { kind: SoftBreak }\r\n   *   { kind: PlainText, text: \"   words\" },\r\n   *   { kind: SoftBreak }\r\n   *   { kind: InlineTag, text: \"{\\@inheritDoc}\" },\r\n   *   { kind: PlainText, text: \"to process.\" },\r\n   *   { kind: PlainText, text: \"  \" },\r\n   *   { kind: PlainText, text: \"  \" }\r\n   * ]\r\n   * ```\r\n   *\r\n   * ...to this:\r\n   *\r\n   * ```\r\n   * nodes: [\r\n   *   { kind: PlainText, text: \"Here are some \" },\r\n   *   { kind: PlainText, text: \"words \" },\r\n   *   { kind: InlineTag, text: \"{\\@inheritDoc}\" },\r\n   *   { kind: PlainText, text: \"to process.\" }\r\n   * ]\r\n   * ```\r\n   *\r\n   * Note that in this example, `\"words \"` is not merged with the preceding node because\r\n   * its DocPlainText.excerpt cannot span multiple lines.\r\n   *\r\n   * @param docParagraph - a DocParagraph containing nodes to be transformed\r\n   * @returns The transformed child nodes.\r\n   */\r\n  public static trimSpacesInParagraph(docParagraph: DocParagraph): DocParagraph {\r\n    return TrimSpacesTransform.transform(docParagraph);\r\n  }\r\n}\r\n"]}