{"version": 3, "file": "ParserMessage.js", "sourceRoot": "", "sources": ["../../src/parser/ParserMessage.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAkB3D;;GAEG;AACH;IAmBE,uBAAmB,UAAoC;QACrD,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;QACtC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,WAAW,CAAC;QAC9C,IAAI,CAAC,SAAS,GAAG,UAAU,CAAC,SAAS,CAAC;QACtC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,CAAC;QAC9C,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;QAClC,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;IACzB,CAAC;IAED;;;;;;OAMG;IACY,gCAAkB,GAAjC,UAAkC,OAAe,EAAE,KAAgB;QACjE,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,GAAG,2BAA2B,CAAC;QACxC,CAAC;QAED,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;YACvC,wFAAwF;YACxF,qDAAqD;YACrD,IAAM,UAAQ,GAAkB,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAC7D,IAAI,UAAQ,CAAC,IAAI,EAAE,CAAC;gBAClB,OAAO,WAAI,UAAQ,CAAC,IAAI,cAAI,UAAQ,CAAC,MAAM,QAAK,GAAG,OAAO,CAAC;YAC7D,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,sBAAW,+BAAI;QAHf;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gBAC7B,wFAAwF;gBACxF,qDAAqD;gBACrD,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACtF,CAAC;YACD,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAEM,gCAAQ,GAAf;QACE,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IACH,oBAAC;AAAD,CAAC,AAlED,IAkEC;AAlEY,sCAAa", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\r\n// See LICENSE in the project root for license information.\r\n\r\nimport type { TextRange, ITextLocation } from './TextRange';\r\nimport type { TokenSequence } from './TokenSequence';\r\nimport type { DocNode } from '../nodes/DocNode';\r\nimport type { TSDocMessageId } from './TSDocMessageId';\r\n\r\n/**\r\n * Constructor parameters for {@link ParserMessage}.\r\n */\r\nexport interface IParserMessageParameters {\r\n  messageId: TSDocMessageId;\r\n  messageText: string;\r\n  textRange: TextRange;\r\n  tokenSequence?: TokenSequence;\r\n  docNode?: DocNode;\r\n}\r\n\r\n/**\r\n * Represents an error or warning that occurred during parsing.\r\n */\r\nexport class ParserMessage {\r\n  /**\r\n   * A string that uniquely identifies the messages reported by the TSDoc parser.\r\n   */\r\n  public readonly messageId: TSDocMessageId;\r\n\r\n  /**\r\n   * The message text without the default prefix that shows line/column information.\r\n   */\r\n  public readonly unformattedText: string;\r\n\r\n  public readonly textRange: TextRange;\r\n\r\n  public readonly tokenSequence: TokenSequence | undefined;\r\n\r\n  public readonly docNode: DocNode | undefined;\r\n\r\n  private _text: string | undefined;\r\n\r\n  public constructor(parameters: IParserMessageParameters) {\r\n    this.messageId = parameters.messageId;\r\n    this.unformattedText = parameters.messageText;\r\n    this.textRange = parameters.textRange;\r\n    this.tokenSequence = parameters.tokenSequence;\r\n    this.docNode = parameters.docNode;\r\n    this._text = undefined;\r\n  }\r\n\r\n  /**\r\n   * Generates a line/column prefix.  Example with line=2 and column=5\r\n   * and message=\"An error occurred\":\r\n   * ```\r\n   * \"(2,5): An error occurred\"\r\n   * ```\r\n   */\r\n  private static _formatMessageText(message: string, range: TextRange): string {\r\n    if (!message) {\r\n      message = 'An unknown error occurred';\r\n    }\r\n\r\n    if (range.pos !== 0 || range.end !== 0) {\r\n      // NOTE: This currently a potentially expensive operation, since TSDoc currently doesn't\r\n      // have a full newline analysis for the input buffer.\r\n      const location: ITextLocation = range.getLocation(range.pos);\r\n      if (location.line) {\r\n        return `(${location.line},${location.column}): ` + message;\r\n      }\r\n    }\r\n    return message;\r\n  }\r\n\r\n  /**\r\n   * The message text.\r\n   */\r\n  public get text(): string {\r\n    if (this._text === undefined) {\r\n      // NOTE: This currently a potentially expensive operation, since TSDoc currently doesn't\r\n      // have a full newline analysis for the input buffer.\r\n      this._text = ParserMessage._formatMessageText(this.unformattedText, this.textRange);\r\n    }\r\n    return this._text;\r\n  }\r\n\r\n  public toString(): string {\r\n    return this.text;\r\n  }\r\n}\r\n"]}