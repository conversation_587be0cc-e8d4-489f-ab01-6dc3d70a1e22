/*
	MIT License http://www.opensource.org/licenses/mit-license.php
	Author <PERSON> @sokra
*/
/* globals __webpack_hash__ */
if (module.hot) {
	/** @type {undefined|string} */
	var lastHash;
	var upToDate = function upToDate() {
		return /** @type {string} */ (lastHash).indexOf(__webpack_hash__) >= 0;
	};
	var log = require("./log");
	var check = function check() {
		module.hot
			.check()
			.then(function (updatedModules) {
				if (!updatedModules) {
					log("warning", "[HMR] Cannot find update. Need to do a full reload!");
					log(
						"warning",
						"[HMR] (Probably because of restarting the webpack-dev-server)"
					);
					return;
				}

				return module.hot
					.apply({
						ignoreUnaccepted: true,
						ignoreDeclined: true,
						ignoreErrored: true,
						onUnaccepted: function (data) {
							log(
								"warning",
								"Ignored an update to unaccepted module " +
									data.chain.join(" -> ")
							);
						},
						onDeclined: function (data) {
							log(
								"warning",
								"Ignored an update to declined module " +
									data.chain.join(" -> ")
							);
						},
						onErrored: function (data) {
							log("error", data.error);
							log(
								"warning",
								"Ignored an error while updating module " +
									data.moduleId +
									" (" +
									data.type +
									")"
							);
						}
					})
					.then(function (renewedModules) {
						if (!upToDate()) {
							check();
						}

						require("./log-apply-result")(updatedModules, renewedModules);

						if (upToDate()) {
							log("info", "[HMR] App is up to date.");
						}
					});
			})
			.catch(function (err) {
				var status = module.hot.status();
				if (["abort", "fail"].indexOf(status) >= 0) {
					log(
						"warning",
						"[HMR] Cannot check for update. Need to do a full reload!"
					);
					log("warning", "[HMR] " + log.formatError(err));
				} else {
					log("warning", "[HMR] Update check failed: " + log.formatError(err));
				}
			});
	};
	var hotEmitter = require("./emitter");
	hotEmitter.on("webpackHotUpdate", function (currentHash) {
		lastHash = currentHash;
		if (!upToDate()) {
			var status = module.hot.status();
			if (status === "idle") {
				log("info", "[HMR] Checking for updates on the server...");
				check();
			} else if (["abort", "fail"].indexOf(status) >= 0) {
				log(
					"warning",
					"[HMR] Cannot apply update as a previous update " +
						status +
						"ed. Need to do a full reload!"
				);
			}
		}
	});
	log("info", "[HMR] Waiting for update signal from WDS...");
} else {
	throw new Error("[HMR] Hot Module Replacement is disabled.");
}
